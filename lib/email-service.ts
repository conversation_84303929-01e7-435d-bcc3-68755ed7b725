// Client-side email service that calls the server API endpoints

import { Invitation } from "./firebase/invitation-service"

export interface EmailResult {
  success: boolean
  error?: string
  messageId?: string
}

/**
 * Generate an invitation link
 */
export const generateInvitationLink = (invitationId: string): string => {
  // Use the public invitation route that handles redirects
  // The public route will redirect to the appropriate authenticated route after login/signup
  return `${process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"}/invitation/${invitationId}`
}

/**
 * Send an invitation email via the server API
 */
export const sendInvitationEmail = async (
  invitation: Invitation,
  _invitationLink: string, // Not used here as the server generates the link
  templateId?: number // Optional template ID to use
): Promise<EmailResult> => {
  try {
    // Get the auth token
    const token = localStorage.getItem("authToken")
    if (!token) {
      return {
        success: false,
        error: "Authentication required to send emails",
      }
    }

    // Call our API endpoint for sending invitation emails
    const response = await fetch("/api/email/invitation", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        invitationId: invitation.id,
        templateId: templateId,
        inviteeEmail: invitation.inviteeEmail,
      }),
    })

    let data
    try {
      data = await response.json()
    } catch (parseError) {
      console.error("Failed to parse API response:", parseError)
      return {
        success: false,
        error: `API response parsing failed: ${response.status} ${response.statusText}`,
      }
    }

    if (response.ok) {
      return {
        success: true,
        messageId: data.messageId || `email-${Date.now()}`,
      }
    } else {
      console.error("Invitation email sending failed:", {
        status: response.status,
        statusText: response.statusText,
        data: data,
      })

      // Provide more detailed error information
      const errorMessage = data?.error || "Failed to send invitation email"
      const errorDetails = data?.details ? `: ${data.details}` : ""
      const statusInfo = ` (Status: ${response.status})`

      return {
        success: false,
        error: `${errorMessage}${errorDetails}${statusInfo}`,
      }
    }
  } catch (error) {
    console.error("Error sending invitation email:", error)
    return {
      success: false,
      error: "An unexpected error occurred while sending the invitation email",
    }
  }
}
