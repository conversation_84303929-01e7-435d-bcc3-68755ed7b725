# Task: Create Trip Categories (Created | Going | Interested)

## Overview
Implement trip categorization on the trips page with three tabs: Created, Going, and Interested. This involves:
1. Adding "interested" status to user-trip attendance system
2. Ensuring trips track their creator (createdBy field)
3. Creating new hooks for different trip categories
4. Updating the trips page UI with tabs

## Progress Checklist

### Phase 1: Data Model Updates
- [x] Add "interested" status to UserTripStatus type
- [x] Verify Trip entity has createdBy field (already exists)
- [x] Update attendance-toggle-button to support "interested" status
- [ ] Update user-trip service methods to handle "interested" status

### Phase 2: Hook Development
- [x] Create hook for trips created by user
- [x] Create hook for trips user is interested in
- [x] Modify existing useUserAttendingTrips to be more specific (going only)
- [ ] Test all hooks work correctly

### Phase 3: UI Implementation
- [ ] Add tabs component to trips page (Created | Going | Interested)
- [ ] Update trips page to use category-specific hooks
- [ ] Ensure same trip card design is used for all categories
- [ ] Handle empty states for each category
- [ ] Test responsive design

### Phase 4: Testing & Polish
- [ ] Test attendance toggle with new "interested" status
- [ ] Verify trip categorization works correctly
- [ ] Test edge cases (user creates trip but marks as not-going, etc.)
- [ ] Ensure proper loading states
- [ ] Test with different user scenarios

## Implementation Notes
- Current attendance statuses: "going" | "not-going" | "undecided"
- Need to add: "interested" 
- Trip entity already has createdBy field
- useUserTrips currently gets all trips user is associated with
- Need separate hooks for different categories

## Files to Modify
1. `lib/domains/user-trip/user-trip.types.ts` - Add "interested" status
2. `app/(authenticated)/trips/[id]/components/shared/attendance-toggle-button.tsx` - Add interested option
3. `lib/domains/user-trip/user-trip.hooks.ts` - Add new category hooks
4. `lib/domains/user-trip/user-trip.service.ts` - Update service methods
5. `app/(authenticated)/trips/page.tsx` - Add tabs and category filtering
