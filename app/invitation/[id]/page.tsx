"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON>ara<PERSON>, useRout<PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Check, X, Users, LogIn, AlertTriangle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import {
  updateInvitation,
  addMemberToSquad,
  type Invitation,
  getSquad,
} from "@/lib/firebase-service"
import { PageLoading } from "@/components/page-loading"
import {
  useRealtimeInvitationLink,
  useSendInvitationEmails,
} from "@/lib/domains/invitation/invitation.hooks"
import { InvitationService } from "@/lib/domains/invitation/invitation.service"
import { SquadService } from "@/lib/domains/squad/squad.service"
import { InvitationSendService } from "@/lib/domains/invitation/invitation-send.service"

export default function InvitationPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user, loading: authLoading } = useAuthStatus()
  const [loading, setLoading] = useState(true)
  const [invitationLink, setInvitationLink] = useState<any>(null)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isExpired, setIsExpired] = useState(false)

  // First useEffect - fetch invitation data using API endpoint
  useEffect(() => {
    const fetchInvitationData = async () => {
      if (!params.id) {
        setError("Invalid invitation link.")
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const invitationId = params.id as string

        // Fetch invitation data using the API endpoint (works without authentication)
        const response = await fetch(`/api/invitation/${invitationId}`)

        if (!response.ok) {
          if (response.status === 410) {
            setIsExpired(true)
            setError("This invitation has expired or is no longer active.")
          } else if (response.status === 404) {
            setError("Invitation not found or has been cancelled.")
          } else {
            const errorData = await response.json()
            setError(errorData.error || "Failed to fetch invitation details.")
          }
          setLoading(false)
          return
        }

        const invitationData = await response.json()

        // For invitation links, check expiration on the client side as well
        if (invitationData.type === "invitation_link" && invitationData.expiresAt) {
          const now = new Date()
          const expiresAt = new Date(invitationData.expiresAt.seconds * 1000) // Convert Firestore timestamp

          if (expiresAt <= now || !invitationData.isActive) {
            setIsExpired(true)
            setError("This invitation has expired.")
            setLoading(false)
            return
          }
        }

        setInvitationLink(invitationData)
        setLoading(false)
      } catch (error) {
        console.error("Error fetching invitation:", error)
        // Generic error message for users
        setError("There was a problem with this invitation. Please try again later.")
        setLoading(false)
      }
    }

    fetchInvitationData()
  }, [params.id])

  // Second useEffect - handle redirection for non-logged in users and email validation
  useEffect(() => {
    if (!authLoading && !loading && invitationLink) {
      // If user is not logged in, redirect to login
      if (!user) {
        console.log("User not logged in, redirecting to login")
        router.push(
          `/login?callback=${encodeURIComponent(`/invitation/${params.id}`)}&message=invitation_access`
        )
      } else {
        console.log("User logged in:", user.email)

        // For legacy invitations, validate that the logged-in user's email matches the invitation
        if (invitationLink.type === "legacy_invitation" && invitationLink.inviteeEmail) {
          const userEmail = user.email?.toLowerCase()
          const inviteeEmail = invitationLink.inviteeEmail.toLowerCase()

          if (userEmail !== inviteeEmail) {
            setError(
              `This invitation is specifically for ${invitationLink.inviteeEmail}. You are logged in as ${user.email}. Please log in with the correct account or contact the squad leader.`
            )
            setLoading(false)
            return
          }
        }
      }
    }
  }, [authLoading, loading, user, invitationLink, params.id, router])

  const handleJoinSquad = async () => {
    if (!user || !invitationLink) {
      toast({
        title: "Error",
        description: "Unable to process invitation. Please try again later.",
        variant: "destructive",
      })
      return
    }

    // Double-check email validation for legacy invitations
    if (invitationLink.type === "legacy_invitation" && invitationLink.inviteeEmail) {
      const userEmail = user.email?.toLowerCase()
      const inviteeEmail = invitationLink.inviteeEmail.toLowerCase()

      if (userEmail !== inviteeEmail) {
        toast({
          title: "Email Mismatch",
          description: `This invitation is for ${invitationLink.inviteeEmail}. Please log in with the correct account.`,
          variant: "destructive",
        })
        return
      }
    }

    try {
      setProcessing(true)

      // Check if the squad still exists
      const squad = await SquadService.getSquad(invitationLink.squadId)

      if (!squad) {
        toast({
          title: "Squad not found",
          description: "The squad no longer exists.",
          variant: "destructive",
        })
        setProcessing(false)
        return
      }

      // Check if user is already a member of the squad
      const isMember = await SquadService.isUserSquadMember(user.uid, invitationLink.squadId)
      if (isMember) {
        toast({
          title: "Already a member",
          description: `You are already a member of ${invitationLink.squadName}`,
        })

        // Redirect to the squad page
        router.push(`/squads/${invitationLink.squadId}`)
        return
      }

      // Determine join method based on invitation type
      const joinMethod =
        invitationLink.type === "legacy_invitation" ? "email_invitation" : "shareable_link"

      // Add user to squad with join tracking
      const result = await SquadService.addMemberWithTracking(
        invitationLink.squadId,
        user.uid,
        user.email || "",
        user.displayName || "Unknown User",
        joinMethod,
        invitationLink.id,
        invitationLink.inviterId
      )

      if (result.success) {
        console.log(`Successfully joined squad via ${joinMethod}:`, {
          squadId: invitationLink.squadId,
          userId: user.uid,
          joinMethod,
          invitationId: invitationLink.id,
        })

        toast({
          title: "Welcome to the squad!",
          description: `You have successfully joined ${invitationLink.squadName}`,
        })

        // Redirect to the squad page
        router.push(`/squads/${invitationLink.squadId}`)
      } else {
        throw new Error("Failed to join squad")
      }
    } catch (error) {
      console.error("Error joining squad:", error)

      // Generic error message for users
      toast({
        title: "Error joining squad",
        description: "We couldn't add you to the squad at this time. Please try again later.",
        variant: "destructive",
      })

      // Set a generic error state
      setError("There was a problem joining this squad. Please try again later.")
    } finally {
      setProcessing(false)
    }
  }

  if (authLoading || loading) {
    return <PageLoading message="Loading invitation..." />
  }

  if (error) {
    const isExpiredError = isExpired || (error && error.includes("expired"))

    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>{isExpiredError ? "Invitation Expired" : "Invitation Error"}</CardTitle>
            <CardDescription>
              {isExpiredError
                ? "This invitation link is no longer valid"
                : "There was a problem with this invitation"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">
                {isExpiredError
                  ? "This invitation has expired or is no longer valid."
                  : "We couldn't process this invitation at this time."}
              </p>
            </div>
            <p className="text-muted-foreground mt-2">
              {isExpiredError
                ? "Please contact the squad leader to request a new invitation."
                : "Please try again later or contact the squad leader for assistance."}
            </p>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            {user ? (
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>
            ) : (
              <Link
                href={`/login?callback=${encodeURIComponent(`/invitation/${params.id}`)}&message=invitation_access`}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  <LogIn className="mr-2 h-4 w-4" /> Log In to View Invitation
                </Button>
              </Link>
            )}
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (!invitationLink) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Not Found</CardTitle>
            <CardDescription>
              This invitation may have been cancelled or doesn't exist
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">
                The invitation you're looking for could not be found.
              </p>
            </div>
            <p className="text-muted-foreground mt-2">
              This may be because the invitation was cancelled, expired, or the link is incorrect.
            </p>
          </CardContent>
          <CardFooter>
            {user ? (
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>
            ) : (
              <Link href="/login" className="w-full">
                <Button variant="outline" className="w-full">
                  <LogIn className="mr-2 h-4 w-4" /> Log In
                </Button>
              </Link>
            )}
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Format expiration date
  const formatExpirationDate = (expiresAt: any) => {
    if (!expiresAt) return ""
    const date = expiresAt.toDate ? expiresAt.toDate() : new Date(expiresAt)
    return date.toLocaleDateString()
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Squad Invitation</CardTitle>
          <CardDescription>You've been invited to join {invitationLink.squadName}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-primary/10 p-2 rounded-full">
              <Users className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p>
                <span className="font-medium">{invitationLink.inviterName}</span> has invited you to
                join their squad.
              </p>
            </div>
          </div>

          <p className="text-muted-foreground mb-4">
            Join this squad to plan trips and adventures together.
          </p>

          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
            <AlertTriangle className="h-4 w-4" />
            <span>This invitation expires on {formatExpirationDate(invitationLink.expiresAt)}</span>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Link href="/dashboard">
            <Button variant="outline">
              <X className="mr-2 h-4 w-4" /> Not Now
            </Button>
          </Link>
          <Button onClick={handleJoinSquad} disabled={processing}>
            <Check className="mr-2 h-4 w-4" />
            {processing ? "Joining..." : "Join Squad"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
