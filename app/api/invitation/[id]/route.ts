import { NextRequest, NextResponse } from "next/server"
import { getInvitationById } from "@/lib/server/invitation-service"
import { getAdminInstance } from "@/lib/firebase-admin"

/**
 * API route to fetch invitation details using Firebase Admin SDK
 * This route does not require authentication and supports both legacy invitations and invitation links
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: invitationId } = await params

    if (!invitationId) {
      return NextResponse.json({ error: "Invitation ID is required" }, { status: 400 })
    }

    // First try to get legacy invitation
    const legacyInvitation = await getInvitationById(invitationId)
    if (legacyInvitation) {
      // Return a safe version of the legacy invitation
      return NextResponse.json({
        id: legacyInvitation.id,
        squadId: legacyInvitation.squadId,
        squadName: legacyInvitation.squadName,
        inviteeId: legacyInvitation.inviteeId,
        inviterName: legacyInvitation.inviterName,
        inviteeEmail: legacyInvitation.inviteeEmail,
        status: legacyInvitation.status,
        createdAt: legacyInvitation.createdAt,
        lastUpdated: legacyInvitation.lastUpdated,
        type: "legacy_invitation",
      })
    }

    // Then try to get invitation link
    const { adminDb } = getAdminInstance()
    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()

    if (invitationLinkDoc.exists) {
      const invitationLinkData = invitationLinkDoc.data()

      // Check if the invitation is expired or inactive
      const now = new Date()
      const expiresAt = invitationLinkData?.expiresAt?.toDate()

      if (!invitationLinkData?.isActive || (expiresAt && expiresAt <= now)) {
        return NextResponse.json(
          { error: "Invitation has expired or is no longer active" },
          { status: 410 }
        )
      }

      // Return invitation link data in a format compatible with the invitation page
      const invitationLink = {
        id: invitationLinkDoc.id,
        squadId: invitationLinkData?.squadId,
        squadName: invitationLinkData?.squadName,
        inviterId: invitationLinkData?.inviterId,
        inviterName: invitationLinkData?.inviterName,
        expiresAt: invitationLinkData?.expiresAt,
        isActive: invitationLinkData?.isActive,
        createdAt: invitationLinkData?.createdAt,
        type: "invitation_link", // Add type to distinguish from legacy invitations
      }

      return NextResponse.json(invitationLink)
    }

    return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
  } catch (error) {
    console.error("Error fetching invitation:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch invitation details",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
