import { NextRequest, NextResponse } from "next/server"
import { getInvitationById } from "@/lib/server/invitation-service"
import { getAdminInstance } from "@/lib/firebase-admin"

/**
 * Public API route to fetch minimal invitation details for redirect logic
 * This route does not require authentication but only returns essential data
 * for determining the appropriate redirect flow
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: invitationId } = await params

    if (!invitationId) {
      return NextResponse.json({ error: "Invitation ID is required" }, { status: 400 })
    }

    // First try to get legacy invitation
    const legacyInvitation = await getInvitationById(invitationId)
    if (legacyInvitation) {
      // Return minimal data for legacy invitation
      return NextResponse.json({
        id: legacyInvitation.id,
        type: "legacy_invitation",
        inviteeEmail: legacyInvitation.inviteeEmail,
        squadName: legacyInvitation.squadName,
        inviterName: legacyInvitation.inviterName
      })
    }

    // Then try to get invitation link
    const { adminDb } = await getAdminInstance()
    
    if (!adminDb) {
      console.error("Firebase Admin Firestore is not initialized")
      return NextResponse.json({ error: "Internal server error" }, { status: 500 })
    }
    
    const invitationLinkDoc = await adminDb.collection("invitation-links").doc(invitationId).get()
    
    if (invitationLinkDoc.exists) {
      const invitationLinkData = invitationLinkDoc.data()
      
      // Check if the invitation is expired or inactive
      const now = new Date()
      const expiresAt = invitationLinkData?.expiresAt?.toDate()
      
      if (!invitationLinkData?.isActive || (expiresAt && expiresAt <= now)) {
        return NextResponse.json(
          { error: "Invitation has expired or is no longer active" },
          { status: 410 }
        )
      }

      // Return minimal data for invitation link
      return NextResponse.json({
        id: invitationLinkDoc.id,
        type: "invitation_link",
        squadName: invitationLinkData?.squadName,
        inviterName: invitationLinkData?.inviterName
      })
    }

    return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
  } catch (error) {
    console.error("Error fetching public invitation:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch invitation details",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
