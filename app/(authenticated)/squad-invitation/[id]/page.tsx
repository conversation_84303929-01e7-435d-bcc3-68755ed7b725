"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Check, X, Users, LogIn, AlertTriangle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { auth } from "@/lib/firebase"
import { PageLoading } from "@/components/page-loading"
import { SquadService } from "@/lib/domains/squad/squad.service"

export default function InvitationPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user, loading: authLoading } = useAuthStatus()
  const [loading, setLoading] = useState(true)
  const [invitationLink, setInvitationLink] = useState<any>(null)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isExpired, setIsExpired] = useState(false)
  const [specificInvitee, setSpecificInvitee] = useState<any>(null)

  // First useEffect - fetch invitation data using API endpoint
  useEffect(() => {
    const fetchInvitationData = async () => {
      if (!params.id) {
        setError("Invalid invitation link.")
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const invitationId = params.id as string

        // Fetch invitation data using the authenticated API endpoint
        const token = await auth.currentUser?.getIdToken()
        const response = await fetch(`/api/invitation/${invitationId}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        if (!response.ok) {
          if (response.status === 410) {
            setIsExpired(true)
            setError("This invitation has expired or is no longer active.")
          } else if (response.status === 404) {
            setError("Invitation not found or has been cancelled.")
          } else {
            const errorData = await response.json()
            setError(errorData.error || "Failed to fetch invitation details.")
          }
          setLoading(false)
          return
        }

        const invitationData = await response.json()

        // For invitation links, check expiration on the client side as well
        if (invitationData.type === "invitation_link" && invitationData.expiresAt) {
          const now = new Date()
          const expiresAt = new Date(invitationData.expiresAt.seconds * 1000) // Convert Firestore timestamp

          if (expiresAt <= now || !invitationData.isActive) {
            setIsExpired(true)
            setError("This invitation has expired.")
            setLoading(false)
            return
          }
        }

        setInvitationLink(invitationData)
        setLoading(false)
      } catch (error) {
        console.error("Error fetching invitation:", error)
        // Generic error message for users
        setError("There was a problem with this invitation. Please try again later.")
        setLoading(false)
      }
    }

    fetchInvitationData()
  }, [params.id])

  // Second useEffect - handle email validation for logged-in users
  useEffect(() => {
    if (!authLoading && !loading && invitationLink && user) {
      console.log("User logged in:", user.email)

      // Check if there's a specific sendId in the URL
      const urlParams = new URLSearchParams(window.location.search)
      const sendId = urlParams.get("sendId")

      if (sendId && invitationLink.invitationSends?.length > 0) {
        // Find the specific invitation send
        const specificSend = invitationLink.invitationSends.find((send: any) => send.id === sendId)

        if (specificSend) {
          setSpecificInvitee(specificSend)

          // Validate that the logged-in user's email matches the specific invitee
          const userEmail = user.email?.toLowerCase()
          const inviteeEmail = specificSend.email.toLowerCase()

          if (userEmail !== inviteeEmail) {
            setError(
              `This invitation is specifically for ${specificSend.email}. You are logged in as ${user.email}. Please log in with the correct account or contact the squad leader.`
            )
            setLoading(false)
            return
          }
        } else {
          setError("Invalid invitation link. The specific invitation could not be found.")
          setLoading(false)
          return
        }
      } else if (invitationLink.invitationSends?.length > 0) {
        // General validation for invitation links with specific invitees (no sendId)
        const userEmail = user.email?.toLowerCase()
        const inviteeEmails = invitationLink.invitationSends.map((send: any) =>
          send.email.toLowerCase()
        )

        if (!inviteeEmails.includes(userEmail)) {
          const emailList = inviteeEmails.join(", ")
          setError(
            `This invitation is specifically for: ${emailList}. You are logged in as ${user.email}. Please log in with the correct account or contact the squad leader.`
          )
          setLoading(false)
          return
        }
      }
    }
  }, [authLoading, loading, user, invitationLink])

  const handleJoinSquad = async () => {
    if (!user || !invitationLink) {
      toast({
        title: "Error",
        description: "Unable to process invitation. Please try again later.",
        variant: "destructive",
      })
      return
    }

    // Double-check email validation for invitation links with specific invitees
    if (specificInvitee) {
      // Specific invitee validation
      const userEmail = user.email?.toLowerCase()
      const inviteeEmail = specificInvitee.email.toLowerCase()

      if (userEmail !== inviteeEmail) {
        toast({
          title: "Email Mismatch",
          description: `This invitation is for ${specificInvitee.email}. Please log in with the correct account.`,
          variant: "destructive",
        })
        return
      }
    } else if (
      invitationLink.type === "invitation_link" &&
      invitationLink.invitationSends?.length > 0
    ) {
      // General validation for invitation links with specific invitees
      const userEmail = user.email?.toLowerCase()
      const inviteeEmails = invitationLink.invitationSends.map((send: any) =>
        send.email.toLowerCase()
      )

      if (!inviteeEmails.includes(userEmail)) {
        const emailList = inviteeEmails.join(", ")
        toast({
          title: "Email Mismatch",
          description: `This invitation is for: ${emailList}. Please log in with the correct account.`,
          variant: "destructive",
        })
        return
      }
    }

    try {
      setProcessing(true)

      // Check if the squad still exists
      const squad = await SquadService.getSquad(invitationLink.squadId)

      if (!squad) {
        toast({
          title: "Squad not found",
          description: "The squad no longer exists.",
          variant: "destructive",
        })
        setProcessing(false)
        return
      }

      // Check if user is already a member of the squad
      const isMember = await SquadService.isUserSquadMember(user.uid, invitationLink.squadId)
      if (isMember) {
        toast({
          title: "Already a member",
          description: `You are already a member of ${invitationLink.squadName}`,
        })

        // Redirect to the squad page
        router.push(`/squads/${invitationLink.squadId}`)
        return
      }

      // Determine join method based on whether there are specific invitees
      const joinMethod =
        invitationLink.invitationSends?.length > 0 ? "email_invitation" : "shareable_link"

      // Add user to squad with join tracking
      const result = await SquadService.addMemberWithTracking(
        invitationLink.squadId,
        user.uid,
        user.email || "",
        user.displayName || "Unknown User",
        joinMethod,
        invitationLink.id,
        invitationLink.inviterId
      )

      if (result.success) {
        console.log(`Successfully joined squad via ${joinMethod}:`, {
          squadId: invitationLink.squadId,
          userId: user.uid,
          joinMethod,
          invitationId: invitationLink.id,
        })

        toast({
          title: "Welcome to the squad!",
          description: `You have successfully joined ${invitationLink.squadName}`,
        })

        // Redirect to the squad page
        router.push(`/squads/${invitationLink.squadId}`)
      } else {
        throw new Error("Failed to join squad")
      }
    } catch (error) {
      console.error("Error joining squad:", error)

      // Generic error message for users
      toast({
        title: "Error joining squad",
        description: "We couldn't add you to the squad at this time. Please try again later.",
        variant: "destructive",
      })

      // Set a generic error state
      setError("There was a problem joining this squad. Please try again later.")
    } finally {
      setProcessing(false)
    }
  }

  if (authLoading || loading) {
    return <PageLoading message="Loading invitation..." />
  }

  if (error) {
    const isExpiredError = isExpired || (error && error.includes("expired"))

    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>{isExpiredError ? "Invitation Expired" : "Invitation Error"}</CardTitle>
            <CardDescription>
              {isExpiredError
                ? "This invitation link is no longer valid"
                : "There was a problem with this invitation"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">
                {isExpiredError
                  ? "This invitation has expired or is no longer valid."
                  : "We couldn't process this invitation at this time."}
              </p>
            </div>
            <p className="text-muted-foreground mt-2">
              {isExpiredError
                ? "Please contact the squad leader to request a new invitation."
                : "Please try again later or contact the squad leader for assistance."}
            </p>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            {user ? (
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>
            ) : (
              <Link
                href={`/login?callback=${encodeURIComponent(`/squad-invitation/${params.id}`)}&message=invitation_access`}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  <LogIn className="mr-2 h-4 w-4" /> Log In to View Invitation
                </Button>
              </Link>
            )}
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (!invitationLink) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Not Found</CardTitle>
            <CardDescription>
              This invitation may have been cancelled or doesn't exist
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">
                The invitation you're looking for could not be found.
              </p>
            </div>
            <p className="text-muted-foreground mt-2">
              This may be because the invitation was cancelled, expired, or the link is incorrect.
            </p>
          </CardContent>
          <CardFooter>
            {user ? (
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>
            ) : (
              <Link href="/login" className="w-full">
                <Button variant="outline" className="w-full">
                  <LogIn className="mr-2 h-4 w-4" /> Log In
                </Button>
              </Link>
            )}
          </CardFooter>
        </Card>
      </div>
    )
  }

  // Format expiration date
  const formatExpirationDate = (expiresAt: any) => {
    if (!expiresAt) return ""
    const date = expiresAt.toDate ? expiresAt.toDate() : new Date(expiresAt)
    return date.toLocaleDateString()
  }

  // Show invitation details to both authenticated and unauthenticated users
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Squad Invitation</CardTitle>
          <CardDescription>You've been invited to join {invitationLink.squadName}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-primary/10 p-2 rounded-full">
              <Users className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p>
                <span className="font-medium">{invitationLink.inviterName}</span> has invited you to
                join their squad.
              </p>
            </div>
          </div>

          <p className="text-muted-foreground mb-4">
            Join this squad to plan trips and adventures together.
          </p>

          {invitationLink.expiresAt && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
              <AlertTriangle className="h-4 w-4" />
              <span>
                This invitation expires on {formatExpirationDate(invitationLink.expiresAt)}
              </span>
            </div>
          )}

          {/* Show specific email for invitation links with specific invitees */}
          {specificInvitee ? (
            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg mb-4">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                This invitation is specifically for:{" "}
                <span className="font-medium">{specificInvitee.email}</span>
              </p>
            </div>
          ) : (
            invitationLink.type === "invitation_link" &&
            invitationLink.invitationSends?.length > 0 && (
              <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg mb-4">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  This invitation is specifically for:{" "}
                  <span className="font-medium">
                    {invitationLink.invitationSends.map((send: any) => send.email).join(", ")}
                  </span>
                </p>
              </div>
            )
          )}
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          {user ? (
            // User is logged in - show join/decline options
            <div className="flex justify-between w-full">
              <Link href="/dashboard">
                <Button variant="outline">
                  <X className="mr-2 h-4 w-4" /> Not Now
                </Button>
              </Link>
              <Button onClick={handleJoinSquad} disabled={processing}>
                <Check className="mr-2 h-4 w-4" />
                {processing ? "Joining..." : "Join Squad"}
              </Button>
            </div>
          ) : (
            // This should not happen in authenticated routes, but just in case
            <Link href="/login" className="w-full">
              <Button className="w-full">
                <LogIn className="mr-2 h-4 w-4" /> Please Log In
              </Button>
            </Link>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
